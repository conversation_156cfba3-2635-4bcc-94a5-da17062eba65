<template>
  <EleTooltipButton
    type="danger"
    text
    :loading
    :icon="loading ? '' : 'Delete'"
    placement="top"
    :content="$t('common.remove')"
    @click="handleClick(device)"
  >
  </EleTooltipButton>

  <!-- 配置迁移对话框 -->
  <ConfigMigrationDialog
    ref="configMigrationDialog"
    @success="onMigrationSuccess"
    @skip="onMigrationSkip"
    @cancel="onMigrationCancel"
  />
</template>

<script>
import { sleep } from '$/utils'
import { ScrcpyConfigMigrator } from '$/store/device/helpers/index.js'
import ConfigMigrationDialog from './ConfigMigrationDialog.vue'

export default {
  components: {
    ConfigMigrationDialog,
  },

  props: {
    device: {
      type: Object,
      default: () => ({}),
    },
    handleRefresh: {
      type: Function,
      default: () => {},
    },
  },

  setup() {
    const deviceStore = useDeviceStore()
    return {
      deviceStore,
    }
  },

  data() {
    return {
      loading: false,
    }
  },

  methods: {
    /**
     * 处理点击删除按钮
     */
    async handleClick(device = this.device) {
      this.loading = true

      try {
        // 检查设备是否有 scrcpy 配置
        const migrator = new ScrcpyConfigMigrator()
        const scrcpyConfig = migrator.getScrcpyConfig()
        const hasConfig = migrator.hasConfig(scrcpyConfig, device.id)

        if (hasConfig) {
          // 如果有配置，先检查是否有可迁移的目标设备
          const targetDevices = this.findTargetDevices(device)

          if (targetDevices.length > 0) {
            // 有可迁移的目标设备，显示迁移对话框
            this.loading = false
            this.$refs.configMigrationDialog.open(device)
          }
          else {
            // 没有可迁移的目标设备，直接删除
            await this.removeDevice(device)
          }
        }
        else {
          // 如果没有配置，直接删除
          await this.removeDevice(device)
        }
      }
      catch (error) {
        console.error('Error checking device config:', error)
        // 出错时直接删除设备
        await this.removeDevice(device)
      }
    },

    /**
     * 查找目标设备（通过 serialNo 匹配）
     */
    findTargetDevices(device) {
      if (!device?.serialNo) {
        return []
      }

      // 从当前设备列表中查找具有相同 serialNo 但不同 id 的设备
      return this.deviceStore.list.filter((targetDevice) => {
        return targetDevice.serialNo === device.serialNo
          && targetDevice.id !== device.id
      })
    },

    /**
     * 删除设备
     */
    async removeDevice(device) {
      const devices = { ...window.appStore.get('device') }
      delete devices[device.id]
      window.appStore.set('device', devices)
      this.handleRefresh()
      await sleep()
      this.loading = false
    },

    /**
     * 配置迁移成功回调
     */
    async onMigrationSuccess() {
      await this.removeDevice(this.device)
    },

    /**
     * 跳过配置迁移回调
     */
    async onMigrationSkip() {
      await this.removeDevice(this.device)
    },

    /**
     * 取消配置迁移回调
     */
    onMigrationCancel() {
      this.loading = false
    },
  },
}
</script>

<style></style>
